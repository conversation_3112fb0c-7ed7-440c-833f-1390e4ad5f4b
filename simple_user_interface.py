#!/usr/bin/env python3
"""
Interface sederhana untuk prediksi risiko k<PERSON>
Berdasarkan INPUT DASAR yang mudah diisi user, bukan fitur turunan
"""

import streamlit as st
import sys
import pandas as pd
import numpy as np
from pathlib import Path
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')

try:
    from utils.data_utils import load_model_artifacts
    MODEL_AVAILABLE = True
except:
    MODEL_AVAILABLE = False

# Page config
st.set_page_config(
    page_title="Fatigue Risk Predictor - Simple",
    page_icon="🤖",
    layout="wide"
)

def calculate_derived_features(user_input):
    """
    Menghitung fitur turunan dari input dasar user
    Ini meniru proses feature engineering yang ada di data_processor.py
    """
    
    # Input dasar dari user
    strava_activities = user_input['strava_activities']
    pomokit_cycles = user_input['pomokit_cycles']
    total_distance = user_input['total_distance_km']
    total_time = user_input['total_time_minutes']
    work_days = user_input['work_days']
    activity_days = user_input['activity_days']
    
    # Hitung fitur turunan
    derived = {}
    
    # Distance metrics
    derived['total_distance_km'] = total_distance
    derived['avg_distance_km'] = total_distance / max(activity_days, 1)
    
    # Time metrics  
    derived['total_time_minutes'] = total_time
    derived['avg_time_minutes'] = total_time / max(activity_days, 1)
    
    # Cycle metrics
    derived['avg_cycles'] = pomokit_cycles / max(work_days, 1)
    
    # Efficiency
    derived['weekly_efficiency'] = pomokit_cycles / max(work_days, 1)
    
    # Title-based features (simplified)
    derived['strava_title_count'] = strava_activities
    derived['strava_title_length'] = 15.0  # Average assumption
    derived['strava_unique_words'] = max(1, int(strava_activities * 0.8))  # Estimate
    
    derived['pomokit_title_count'] = pomokit_cycles
    derived['pomokit_title_length'] = 25.0  # Average assumption
    derived['pomokit_unique_words'] = max(1, int(pomokit_cycles * 0.7))  # Estimate
    
    derived['total_title_diversity'] = derived['strava_unique_words'] + derived['pomokit_unique_words']
    derived['title_balance_ratio'] = derived['strava_title_length'] / max(derived['pomokit_title_length'], 1)
    
    # Gamification features
    WEEKLY_DISTANCE_TARGET = 6  # km
    WEEKLY_CYCLES_TARGET = 5
    MAX_ACTIVITY_POINTS = 100
    MAX_PRODUCTIVITY_POINTS = 100
    
    derived['activity_points'] = min(
        (total_distance / WEEKLY_DISTANCE_TARGET) * MAX_ACTIVITY_POINTS,
        MAX_ACTIVITY_POINTS
    )
    
    derived['productivity_points'] = min(
        (pomokit_cycles / WEEKLY_CYCLES_TARGET) * MAX_PRODUCTIVITY_POINTS,
        MAX_PRODUCTIVITY_POINTS
    )
    
    total_points = derived['activity_points'] + derived['productivity_points']
    derived['achievement_rate'] = total_points / 200  # Max total points
    
    derived['gamification_balance'] = abs(derived['activity_points'] - derived['productivity_points'])
    
    return derived

def load_prediction_model():
    """Load the trained model"""
    if not MODEL_AVAILABLE:
        return None, None, None, None
    
    try:
        artifacts = load_model_artifacts("results/clean_production_model")
        model = artifacts['pipeline']
        features = artifacts['features']
        label_encoder = artifacts['label_encoder']
        metadata = artifacts['metadata']
        return model, features, label_encoder, metadata
    except Exception as e:
        st.error(f"Failed to load model: {str(e)}")
        return None, None, None, None

def create_simple_input_form():
    """Create simple input form based on basic user activities"""
    
    st.header("📋 Input Data Aktivitas Mingguan")
    st.markdown("*Isi data aktivitas Anda dalam seminggu terakhir*")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🏃 Aktivitas Fisik (Strava)")
        
        strava_activities = st.number_input(
            "Jumlah aktivitas olahraga dalam seminggu",
            min_value=0, max_value=20, value=3, step=1,
            help="Berapa kali Anda berolahraga dalam seminggu?"
        )
        
        total_distance = st.number_input(
            "Total jarak olahraga (km)",
            min_value=0.0, max_value=100.0, value=12.0, step=0.5,
            help="Total jarak yang Anda tempuh saat berolahraga dalam seminggu"
        )
        
        total_time = st.number_input(
            "Total waktu olahraga (menit)",
            min_value=0, max_value=2000, value=180, step=15,
            help="Total waktu yang Anda habiskan untuk berolahraga dalam seminggu"
        )
        
        activity_days = st.number_input(
            "Berapa hari Anda berolahraga?",
            min_value=0, max_value=7, value=3, step=1,
            help="Jumlah hari dalam seminggu Anda melakukan aktivitas fisik"
        )
    
    with col2:
        st.subheader("💼 Aktivitas Produktivitas (Pomokit)")
        
        pomokit_cycles = st.number_input(
            "Jumlah sesi kerja/belajar dalam seminggu",
            min_value=0, max_value=50, value=15, step=1,
            help="Berapa sesi kerja/belajar yang Anda lakukan dalam seminggu?"
        )
        
        work_days = st.number_input(
            "Berapa hari Anda bekerja/belajar?",
            min_value=0, max_value=7, value=5, step=1,
            help="Jumlah hari dalam seminggu Anda bekerja atau belajar"
        )
        
        # Additional context questions
        st.subheader("📊 Konteks Tambahan")
        
        sleep_quality = st.select_slider(
            "Kualitas tidur rata-rata",
            options=["Sangat Buruk", "Buruk", "Cukup", "Baik", "Sangat Baik"],
            value="Baik",
            help="Bagaimana kualitas tidur Anda dalam seminggu terakhir?"
        )
        
        stress_level = st.select_slider(
            "Tingkat stress yang dirasakan",
            options=["Sangat Rendah", "Rendah", "Sedang", "Tinggi", "Sangat Tinggi"],
            value="Sedang",
            help="Seberapa stress Anda dalam seminggu terakhir?"
        )
    
    return {
        'strava_activities': strava_activities,
        'total_distance_km': total_distance,
        'total_time_minutes': total_time,
        'activity_days': activity_days,
        'pomokit_cycles': pomokit_cycles,
        'work_days': work_days,
        'sleep_quality': sleep_quality,
        'stress_level': stress_level
    }

def display_prediction_results(predicted_class, probabilities, metadata, user_input):
    """Display prediction results with visualizations"""
    
    st.header("🔮 Hasil Prediksi Risiko Kelelahan")
    
    # Main prediction result
    col1, col2, col3 = st.columns(3)
    
    # Color coding for risk levels
    colors = {
        'low_risk': '#28a745',      # Green
        'medium_risk': '#ffc107',   # Yellow
        'high_risk': '#dc3545'      # Red
    }
    
    risk_labels = {
        'low_risk': 'Risiko Rendah',
        'medium_risk': 'Risiko Sedang', 
        'high_risk': 'Risiko Tinggi'
    }
    
    with col1:
        st.metric(
            label="🎯 Prediksi Risiko Kelelahan",
            value=risk_labels[predicted_class],
            delta=f"Confidence: {probabilities[predicted_class]*100:.1f}%"
        )
    
    with col2:
        st.metric(
            label="🤖 Model Accuracy",
            value=f"{metadata.get('accuracy', 0)*100:.1f}%",
            delta="Random Forest"
        )
    
    with col3:
        max_prob = max(probabilities.values())
        confidence_level = "Tinggi" if max_prob > 0.8 else "Sedang" if max_prob > 0.6 else "Rendah"
        st.metric(
            label="📊 Confidence Level",
            value=confidence_level,
            delta=f"{max_prob*100:.1f}%"
        )
    
    # Probability visualization
    st.subheader("📈 Distribusi Probabilitas")
    
    # Create probability chart
    prob_df = pd.DataFrame([
        {'Risk Level': risk_labels[k], 'Probability': v*100, 'Color': colors[k]} 
        for k, v in probabilities.items()
    ])
    
    fig = px.bar(
        prob_df, 
        x='Risk Level', 
        y='Probability',
        color='Risk Level',
        color_discrete_map={
            'Risiko Rendah': colors['low_risk'],
            'Risiko Sedang': colors['medium_risk'],
            'Risiko Tinggi': colors['high_risk']
        },
        title="Probabilitas untuk Setiap Tingkat Risiko"
    )
    fig.update_layout(showlegend=False)
    fig.update_yaxis(title="Probabilitas (%)")
    st.plotly_chart(fig, use_container_width=True)
    
    # Activity summary
    st.subheader("📊 Ringkasan Aktivitas Anda")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("🏃 Aktivitas Olahraga", f"{user_input['strava_activities']} kali")
        st.metric("📏 Total Jarak", f"{user_input['total_distance_km']} km")
    
    with col2:
        st.metric("⏱️ Total Waktu Olahraga", f"{user_input['total_time_minutes']} menit")
        st.metric("📅 Hari Aktif", f"{user_input['activity_days']} hari")
    
    with col3:
        st.metric("💼 Sesi Kerja/Belajar", f"{user_input['pomokit_cycles']} sesi")
        st.metric("📅 Hari Kerja", f"{user_input['work_days']} hari")
    
    with col4:
        avg_distance = user_input['total_distance_km'] / max(user_input['activity_days'], 1)
        avg_cycles = user_input['pomokit_cycles'] / max(user_input['work_days'], 1)
        st.metric("📊 Rata-rata Jarak/Hari", f"{avg_distance:.1f} km")
        st.metric("📊 Rata-rata Sesi/Hari", f"{avg_cycles:.1f} sesi")
    
    # Recommendations based on prediction
    st.subheader("💡 Rekomendasi")
    
    if predicted_class == 'high_risk':
        st.error("""
        **⚠️ RISIKO TINGGI TERDETEKSI**
        - Segera istirahat dan kurangi aktivitas berat
        - Monitor kondisi kesehatan secara berkala
        - Pertimbangkan konsultasi dengan profesional kesehatan
        - Hindari aktivitas yang membutuhkan konsentrasi tinggi
        - Perbaiki kualitas tidur dan kurangi stress
        """)
    elif predicted_class == 'medium_risk':
        st.warning("""
        **⚡ RISIKO SEDANG**
        - Perhatikan pola istirahat dan aktivitas
        - Lakukan aktivitas ringan untuk recovery
        - Monitor gejala kelelahan
        - Pertimbangkan mengurangi beban kerja sementara
        - Jaga keseimbangan antara aktivitas fisik dan mental
        """)
    else:
        st.success("""
        **✅ RISIKO RENDAH**
        - Kondisi baik, lanjutkan aktivitas normal
        - Tetap jaga pola istirahat yang sehat
        - Monitor kondisi secara berkala
        - Pertahankan gaya hidup sehat
        - Terus jaga keseimbangan aktivitas
        """)

def main():
    """Main Streamlit app"""
    
    st.title("🤖 Fatigue Risk Prediction System")
    st.markdown("### *Prediksi Risiko Kelelahan Berdasarkan Aktivitas Mingguan*")
    st.markdown("---")
    
    # Load model
    model, features, label_encoder, metadata = load_prediction_model()
    
    if not model:
        st.error("❌ Model tidak dapat dimuat. Pastikan model sudah dilatih dengan menjalankan: `python main1.py --ml-only`")
        return
    
    # Sidebar with model info
    with st.sidebar:
        st.header("ℹ️ Model Information")
        st.write(f"**Algorithm:** {metadata.get('algorithm_name', 'Unknown')}")
        st.write(f"**Accuracy:** {metadata.get('accuracy', 0)*100:.2f}%")
        st.write(f"**Features:** {len(features)}")
        st.write(f"**Classes:** Low, Medium, High Risk")
        
        st.markdown("---")
        st.header("📖 Cara Penggunaan")
        st.write("""
        1. **Aktivitas Fisik**: Isi data olahraga Anda
        2. **Aktivitas Produktivitas**: Isi data kerja/belajar
        3. **Konteks Tambahan**: Isi kualitas tidur & stress
        4. **Klik Prediksi**: Lihat hasil dan rekomendasi
        """)
        
        st.markdown("---")
        st.header("🎯 Tips")
        st.write("""
        - **Jujur** dalam mengisi data
        - **Konsisten** dengan periode seminggu
        - **Perhatikan** rekomendasi yang diberikan
        - **Konsultasi** profesional jika perlu
        """)
    
    # Create input form
    user_input = create_simple_input_form()
    
    # Prediction button
    st.markdown("---")
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        if st.button("🔮 Prediksi Risiko Kelelahan", type="primary", use_container_width=True):
            try:
                # Calculate derived features
                derived_features = calculate_derived_features(user_input)
                
                # Prepare data for prediction
                df = pd.DataFrame([derived_features])
                X = df[features]
                
                # Make prediction
                prediction = model.predict(X)[0]
                probabilities = model.predict_proba(X)[0]
                predicted_class = label_encoder.inverse_transform([prediction])[0]
                
                # Create probabilities dict
                classes = label_encoder.classes_
                prob_dict = {cls: prob for cls, prob in zip(classes, probabilities)}
                
                # Display results
                st.markdown("---")
                display_prediction_results(predicted_class, prob_dict, metadata, user_input)
                
            except Exception as e:
                st.error(f"❌ Error dalam prediksi: {str(e)}")
                st.error("Pastikan semua field terisi dengan benar")

if __name__ == "__main__":
    main()
