"""
Data Utilities - Clean helper functions for data processing
Author: Research Team
Date: 2025-06-28
Purpose: Reusable utility functions for data loading, preprocessing, and validation
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split

logger = logging.getLogger(__name__)


def load_dataset(file_path: Union[str, Path], 
                target_column: str,
                validate: bool = True) -> Tuple[pd.DataFrame, List[str]]:
    """
    Load and validate dataset.
    
    Args:
        file_path: Path to CSV file
        target_column: Name of target variable column
        validate: Whether to perform validation checks
        
    Returns:
        Tuple of (dataframe, feature_columns)
        
    Raises:
        FileNotFoundError: If file doesn't exist
        ValueError: If target column not found or data validation fails
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"Dataset not found: {file_path}")
    
    logger.info(f"Loading dataset from {file_path}")
    df = pd.read_csv(file_path)
    
    if target_column not in df.columns:
        raise ValueError(f"Target column '{target_column}' not found in dataset")
    
    # Get feature columns (exclude target)
    feature_columns = [col for col in df.columns if col != target_column]
    
    if validate:
        _validate_dataset(df, target_column, feature_columns)
    
    logger.info(f"Dataset loaded: {len(df)} samples, {len(feature_columns)} features")
    
    return df, feature_columns


def _validate_dataset(df: pd.DataFrame, 
                     target_column: str, 
                     feature_columns: List[str]) -> None:
    """
    Validate dataset quality and structure.
    
    Args:
        df: Input dataframe
        target_column: Target variable column name
        feature_columns: List of feature column names
        
    Raises:
        ValueError: If validation fails
    """
    # Check for empty dataset
    if len(df) == 0:
        raise ValueError("Dataset is empty")
    
    # Check for missing target values
    if df[target_column].isnull().any():
        raise ValueError("Target column contains missing values")
    
    # Check target distribution
    target_counts = df[target_column].value_counts()
    if len(target_counts) < 2:
        raise ValueError("Target variable must have at least 2 classes")
    
    # Log data quality info
    missing_info = df[feature_columns].isnull().sum()
    if missing_info.sum() > 0:
        logger.warning(f"Missing values found in features: {missing_info[missing_info > 0].to_dict()}")
    
    # Log target distribution
    logger.info(f"Target distribution: {target_counts.to_dict()}")


def get_numeric_features(df: pd.DataFrame, 
                        feature_columns: List[str]) -> List[str]:
    """
    Filter numeric features from feature list.
    
    Args:
        df: Input dataframe
        feature_columns: List of all feature columns
        
    Returns:
        List of numeric feature column names
    """
    numeric_features = []
    
    for col in feature_columns:
        if df[col].dtype in ['int64', 'float64', 'int32', 'float32']:
            numeric_features.append(col)
    
    logger.info(f"Found {len(numeric_features)} numeric features out of {len(feature_columns)}")
    
    return numeric_features


def prepare_features_target(df: pd.DataFrame,
                           feature_columns: List[str],
                           target_column: str,
                           handle_missing: str = 'fill_zero') -> Tuple[np.ndarray, np.ndarray]:
    """
    Prepare feature matrix and target vector for modeling.
    
    Args:
        df: Input dataframe
        feature_columns: List of feature column names
        target_column: Target variable column name
        handle_missing: How to handle missing values ('fill_zero', 'drop', 'mean')
        
    Returns:
        Tuple of (X, y) arrays
        
    Raises:
        ValueError: If invalid handle_missing option
    """
    # Select features
    X_df = df[feature_columns].copy()
    
    # Handle missing values
    if handle_missing == 'fill_zero':
        X_df = X_df.fillna(0)
    elif handle_missing == 'mean':
        X_df = X_df.fillna(X_df.mean())
    elif handle_missing == 'drop':
        initial_len = len(X_df)
        X_df = X_df.dropna()
        if len(X_df) < initial_len:
            logger.warning(f"Dropped {initial_len - len(X_df)} rows due to missing values")
    else:
        raise ValueError(f"Invalid handle_missing option: {handle_missing}")
    
    # Convert to arrays
    X = X_df.values
    
    # Encode target
    le = LabelEncoder()
    y = le.fit_transform(df.loc[X_df.index, target_column])
    
    logger.info(f"Prepared data: X shape {X.shape}, y shape {y.shape}")
    
    return X, y


def split_data(X: np.ndarray, 
               y: np.ndarray,
               test_size: float = 0.2,
               random_state: int = 42,
               stratify: bool = True) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    Split data into train and test sets.
    
    Args:
        X: Feature matrix
        y: Target vector
        test_size: Proportion of data for testing
        random_state: Random seed
        stratify: Whether to stratify split by target
        
    Returns:
        Tuple of (X_train, X_test, y_train, y_test)
    """
    stratify_param = y if stratify else None
    
    X_train, X_test, y_train, y_test = train_test_split(
        X, y,
        test_size=test_size,
        random_state=random_state,
        stratify=stratify_param
    )
    
    logger.info(f"Data split: train {X_train.shape[0]}, test {X_test.shape[0]}")
    
    return X_train, X_test, y_train, y_test


def scale_features(X_train: np.ndarray, 
                  X_test: Optional[np.ndarray] = None) -> Tuple[np.ndarray, Optional[np.ndarray], StandardScaler]:
    """
    Scale features using StandardScaler.
    
    Args:
        X_train: Training feature matrix
        X_test: Test feature matrix (optional)
        
    Returns:
        Tuple of (X_train_scaled, X_test_scaled, scaler)
    """
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    
    X_test_scaled = None
    if X_test is not None:
        X_test_scaled = scaler.transform(X_test)
    
    logger.info("Feature scaling completed")
    
    return X_train_scaled, X_test_scaled, scaler


def get_feature_statistics(df: pd.DataFrame, 
                          feature_columns: List[str]) -> pd.DataFrame:
    """
    Get descriptive statistics for features.
    
    Args:
        df: Input dataframe
        feature_columns: List of feature column names
        
    Returns:
        DataFrame with feature statistics
    """
    stats_df = df[feature_columns].describe()
    
    # Add missing value counts
    stats_df.loc['missing'] = df[feature_columns].isnull().sum()
    
    # Add data types
    stats_df.loc['dtype'] = df[feature_columns].dtypes
    
    return stats_df


def check_data_quality(df: pd.DataFrame, 
                      feature_columns: List[str],
                      target_column: str) -> Dict[str, any]:
    """
    Comprehensive data quality check.
    
    Args:
        df: Input dataframe
        feature_columns: List of feature column names
        target_column: Target variable column name
        
    Returns:
        Dictionary with data quality metrics
    """
    quality_report = {
        'total_samples': len(df),
        'total_features': len(feature_columns),
        'missing_values': {},
        'duplicate_rows': df.duplicated().sum(),
        'target_distribution': df[target_column].value_counts().to_dict(),
        'feature_types': {},
        'outliers': {}
    }
    
    # Missing values per feature
    for col in feature_columns:
        missing_count = df[col].isnull().sum()
        if missing_count > 0:
            quality_report['missing_values'][col] = {
                'count': missing_count,
                'percentage': (missing_count / len(df)) * 100
            }
    
    # Feature types
    for col in feature_columns:
        quality_report['feature_types'][col] = str(df[col].dtype)
    
    # Outliers (using IQR method for numeric features)
    numeric_features = get_numeric_features(df, feature_columns)
    for col in numeric_features:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
        if len(outliers) > 0:
            quality_report['outliers'][col] = {
                'count': len(outliers),
                'percentage': (len(outliers) / len(df)) * 100
            }
    
    return quality_report


def save_processed_data(df: pd.DataFrame, 
                       output_path: Union[str, Path],
                       include_timestamp: bool = True) -> str:
    """
    Save processed dataframe to CSV.
    
    Args:
        df: Dataframe to save
        output_path: Output file path
        include_timestamp: Whether to include timestamp in filename
        
    Returns:
        Actual file path where data was saved
    """
    output_path = Path(output_path)
    
    if include_timestamp:
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        stem = output_path.stem
        suffix = output_path.suffix
        output_path = output_path.parent / f"{stem}_{timestamp}{suffix}"
    
    # Create directory if it doesn't exist
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save data
    df.to_csv(output_path, index=False)
    
    logger.info(f"Data saved to {output_path}")
    
    return str(output_path)


def load_model_artifacts(model_dir: Union[str, Path]) -> Dict[str, any]:
    """
    Load saved model artifacts (model, encoder, features, etc.).
    
    Args:
        model_dir: Directory containing model artifacts
        
    Returns:
        Dictionary with loaded artifacts
        
    Raises:
        FileNotFoundError: If required files not found
    """
    import joblib
    
    model_dir = Path(model_dir)
    
    if not model_dir.exists():
        raise FileNotFoundError(f"Model directory not found: {model_dir}")
    
    artifacts = {}
    
    # Look for common artifact files
    artifact_patterns = {
        'model': ['*model*.pkl', '*pipeline*.pkl'],
        'encoder': ['*encoder*.pkl', '*label*.pkl'],
        'features': ['*features*.pkl', '*feature*.pkl'],
        'scaler': ['*scaler*.pkl', '*scale*.pkl']
    }
    
    for artifact_type, patterns in artifact_patterns.items():
        for pattern in patterns:
            files = list(model_dir.glob(pattern))
            if files:
                # Use the most recent file if multiple found
                latest_file = max(files, key=lambda f: f.stat().st_mtime)
                artifacts[artifact_type] = joblib.load(latest_file)
                logger.info(f"Loaded {artifact_type} from {latest_file}")
                break
    
    return artifacts
